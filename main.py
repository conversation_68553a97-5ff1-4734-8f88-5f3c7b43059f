import os
import json
from fastapi import FastAP<PERSON>, Request
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jinja2Templates
from starlette.middleware.sessions import SessionMiddleware
from authlib.integrations.starlette_client import OAuth
from sqlalchemy import create_engine, text
from dotenv import load_dotenv

# Carica le variabili d'ambiente dal file .env
load_dotenv()

# --- Configurazione ---

# App FastAPI
app = FastAPI()

# Aggiungi il middleware per la gestione delle sessioni
# La SECRET_KEY è necessaria per firmare i cookie di sessione
SECRET_KEY = os.getenv("SECRET_KEY")
if not SECRET_KEY:
    raise ValueError("SECRET_KEY non impostata nel file .env. Generane una con 'openssl rand -hex 32'")
app.add_middleware(SessionMiddleware, secret_key=SECRET_KEY)

# Template Jinja2
templates = Jinja2Templates(directory="templates")

# Configurazione OAuth per WSO2 con Authlib
WSO2_CLIENT_ID = os.getenv('WSO2_CLIENT_ID')
if not WSO2_CLIENT_ID:
    raise ValueError("WSO2_CLIENT_ID not set in the .env file")

WSO2_CLIENT_SECRET = os.getenv('WSO2_CLIENT_SECRET')
if not WSO2_CLIENT_SECRET:
    raise ValueError("WSO2_CLIENT_SECRET not set in the .env file")

WSO2_BASE_URL = os.getenv('WSO2_BASE_URL')
if not WSO2_BASE_URL:
    raise ValueError("WSO2_BASE_URL not set in the .env file")

oauth = OAuth(app)
oauth.register(
    name='wso2',
    client_id=WSO2_CLIENT_ID,
    client_secret=WSO2_CLIENT_SECRET,
    authorize_url=f'{WSO2_BASE_URL}/oauth2/authorize',
    access_token_url=f'{WSO2_BASE_URL}/oauth2/token',
    userinfo_url=f'{WSO2_BASE_URL}/oauth2/userinfo',
    jwks_uri=f'{WSO2_BASE_URL}/oauth2/jwks',
    client_kwargs={'scope': 'openid email profile'}
)

# Configurazione Database con SQLAlchemy
DATABASE_URL = os.getenv("DATABASE_URL")
if not DATABASE_URL:
    raise ValueError("DATABASE_URL non impostata nel file .env")
engine = create_engine(DATABASE_URL)


# --- Funzioni di Utilità ---

def get_db_connection_status():
    """Verifica la connessione al database e restituisce lo stato."""
    try:
        with engine.connect() as connection:
            # Esegue una semplice query per verificare la connessione
            connection.execute(text("SELECT 1"))
        return "Connected successfully to the database."
    except Exception as e:
        return f"Error connecting to the database: {e}"


# --- Endpoint dell'Applicazione ---

@app.get("/", response_class=HTMLResponse)
async def homepage(request: Request):
    """Mostra la pagina di login se l'utente non è autenticato."""
    user = request.session.get('user')
    if user:
        return RedirectResponse(url='/dashboard')
    return templates.TemplateResponse("login.html", {"request": request})


@app.get("/login")
async def login(request: Request):
    """Redireziona l'utente a WSO2 per l'autenticazione."""
    redirect_uri = request.url_for('auth')
    wso2_client = oauth.create_client('wso2')
    if wso2_client is None:
        raise ValueError("WSO2 client not registered")
    return await wso2_client.authorize_redirect(request, redirect_uri)


@app.get("/auth")
async def auth(request: Request):
    """
    Endpoint di callback dopo l'autenticazione WSO2.
    Recupera il token di accesso e le informazioni dell'utente.
    """
    try:
        wso2_client = oauth.create_client('wso2')
        token = await wso2_client.authorize_access_token(request)
        # Le informazioni dell'utente sono nel campo 'userinfo' del token
        user_info = token.get('userinfo')
        if user_info:
            request.session['user'] = dict(user_info)
        else:
            # Fallback se userinfo non è presente
            request.session['user'] = {'sub': token.get('sub')}
    except Exception as e:
        # Gestione di eventuali errori durante il recupero del token
        return HTMLResponse(f'<h1>Authentication Error</h1><p>{e}</p>', status_code=400)

    return RedirectResponse(url='/dashboard')


@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard(request: Request):
    """
    Mostra la dashboard se l'utente è autenticato, altrimenti
    redireziona alla pagina di login.
    """
    user = request.session.get('user')
    if not user:
        return RedirectResponse(url='/')

    # Verifica lo stato della connessione al DB
    db_status = get_db_connection_status()

    # Passa i dati al template
    context = {
        "request": request,
        "user": user,
        "user_data": json.dumps(user, indent=4), # Per una visualizzazione pulita nel tag <pre>
        "db_status": db_status
    }
    return templates.TemplateResponse("dashboard.html", context)


@app.get("/logout")
async def logout(request: Request):
    """Effettua il logout cancellando la sessione dell'utente."""
    request.session.pop('user', None)
    return RedirectResponse(url='/')

# Esempio di come avviare l'app con uvicorn dalla riga di comando:
# uvicorn main:app --reload
